import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

// Interface for reviews structure
interface ReviewItem {
  mainTitle: string;
  content: Array<{
    title: string;
    content: string;
  }>;
}

@Schema({ timestamps: true })
export class Exchange extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  image: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  exchangeLink: string;

  @Prop({ required: true })
  info: string;

  // Connected users array similar to broker
  @Prop({
    type: [{
      userId: { type: MongooseSchema.Types.ObjectId, ref: 'Auth' },
      accountId: { type: MongooseSchema.Types.ObjectId, ref: 'ManageAccount' },
      accountNo: { type: String },
      accountType: { type: String }
    }],
    default: []
  })
  connectedUsers: Array<{
    userId: MongooseSchema.Types.ObjectId;
    accountId: MongooseSchema.Types.ObjectId;
    accountNo: string;
    accountType: string;
  }>;

  // Rating system similar to broker
  @Prop({ default: 0, min: 0, max: 5 })
  ratings: number;

  @Prop({ default: 0 })
  totalRatings: number;

  @Prop({
    type: [{
      userId: { type: MongooseSchema.Types.ObjectId, ref: 'Auth', required: false },
      rating: { type: Number, required: false, min: 1, max: 5 },
      review: { type: String, default: '' },
      ratedAt: { type: Date, default: Date.now },
      userName: { type: String, default: '' }
    }],
    default: []
  })
  rateExchange: Array<{
    userId: MongooseSchema.Types.ObjectId;
    rating: number;
    review: string;
    ratedAt: Date;
    userName: string;
  }>;

  // Reviews array with mainTitle and multiple title/content objects
  @Prop({
    type: [{
      mainTitle: { type: String, required: true },
      content: [{
        title: { type: String, required: true },
        content: { type: String, required: true }
      }]
    }],
    default: []
  })

  
  reviews: ReviewItem[];

  @Prop({ type: String, default: 'draft' })
  status: string;

  @Prop({ default: null })
  userId: string;
}

export const ExchangeSchema = SchemaFactory.createForClass(Exchange);
