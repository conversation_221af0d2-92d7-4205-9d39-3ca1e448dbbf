import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, ValidateNested, IsNumber, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

// DTO for review content items
export class ReviewContentDto {
  @ApiProperty({ description: 'Title of the content item' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Content text' })
  @IsString()
  content: string;
}

// DTO for review items
export class ReviewItemDto {
  @ApiProperty({ description: 'Main title for the review section' })
  @IsString()
  mainTitle: string;

  @ApiProperty({
    description: 'Array of content items with title and content',
    type: [ReviewContentDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReviewContentDto)
  content: ReviewContentDto[];
}

// DTO for rating exchange
export class RateExchangeDto {
  @ApiProperty({ description: 'Rating value between 1 and 5', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ description: 'Review text', required: false })
  @IsOptional()
  @IsString()
  review?: string;
}

// Create Exchange DTO
export class CreateExchangeDto {
  @ApiProperty({ description: 'Exchange title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Exchange image URL' })
  @IsString()
  image: string;

  @ApiProperty({ description: 'Exchange description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Exchange link URL' })
  @IsString()
  exchangeLink: string;

  @ApiProperty({ description: 'Exchange information' })
  @IsString()
  info: string;

  @ApiProperty({
    description: 'Reviews array with mainTitle and content objects',
    type: [ReviewItemDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReviewItemDto)
  reviews?: ReviewItemDto[];

  @ApiProperty({ description: 'Exchange status', default: 'draft' })
  @IsOptional()
  @IsString()
  status?: string;
}

// Update Exchange DTO
export class UpdateExchangeDto {
  @ApiProperty({ description: 'Exchange title' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: 'Exchange image URL' })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({ description: 'Exchange description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Exchange link URL' })
  @IsOptional()
  @IsString()
  exchangeLink?: string;

  @ApiProperty({ description: 'Exchange information' })
  @IsOptional()
  @IsString()
  info?: string;

  @ApiProperty({
    description: 'Reviews array with mainTitle and content objects',
    type: [ReviewItemDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReviewItemDto)
  reviews?: ReviewItemDto[];

  @ApiProperty({ description: 'Exchange status' })
  @IsOptional()
  @IsString()
  status?: string;
}
