import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import * as mongoose from 'mongoose';

@Schema({ timestamps: true })
export class Auth {
  @Prop()
  email: string;

  @Prop()
  password: string;

  @Prop()
  firstName: string;

  @Prop()
  lastName: string;

  @Prop({ default: 'user' })
  role: string;

  @Prop({ default: 'active', enum: ['active', 'deleted', 'locked'] })
  status: string;

  @Prop({ default: false })
  isEmailVerified: boolean;

  @Prop({ default: null })
  refreshToken: string;

  @Prop({ default: null })
  verificationCode: string;

  @Prop({ default: false })
  resetPassword: boolean;

  @Prop({ default: null })
  loginType: string;

  @Prop({ default: [], ref: 'Auth' })
  level1: Array<string>;

  @Prop({ default: [], ref: 'Auth' })
  level2: Array<string>;

  @Prop({ default: [], ref: 'Auth' })
  level3: Array<string>;

  @Prop()
  referralCode: string;

  @Prop({ default: 0 })
  referralCount: number;

  @Prop({ default: null })
  refferedBy: string;

  @Prop({ default: 'bronze' })
  rank: string;

  @Prop({ default: 0 })
  balance: number;

  @Prop({ default: 0 })
  totalReferralEarnings: number;

  @Prop({ default: 0 })
  totalTradeEarnings: number;

  @Prop({ default: 0 })
  totalRebate: number;

  @Prop({
    type: [
      {
        brokerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Broker' },
        accountId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'ManageAccount',
        },
        accountType: { type: String, default: '' },
      },
    ],
    default: [],
  })
  connectedBrokers: Array<{
    brokerId: Types.ObjectId;
    accountId: Types.ObjectId;
    accountType: string;
  }>;
  @Prop({ default: 0 })
  totalWithdrawn: number;

  @Prop({ default: 0 })
  level1Earnings: number;

  @Prop({ default: 0 })
  level2Earnings: number;

  @Prop({ default: 0 })
  level3Earnings: number;

  @Prop({ default: 0 })
  totalIncentives: number;
}

export const AuthSchema = SchemaFactory.createForClass(Auth);
