import {
  Controller,
  Get,
  Post,
  UseInterceptors,
  UploadedFile,
  UseGuards,
  Param,
  HttpStatus,
  HttpCode,
  Body,
  Request
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { ProfitService } from './profit.service';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { CurrentUser } from '../../Gaurd/user.decorator';
import { ProcessArrayDto } from './dto/create-profit.dto';

@ApiTags('Profit Management')
@Controller('profit')
@UseGuards(JwtAuthGuard)
export class ProfitController {
  constructor(private readonly profitService: ProfitService) {}

  @Post('process-csv/:brokerId')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Process CSV file for profit distribution',
    description: 'Upload CSV file with trading data to calculate and distribute profits for specific broker'
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: 'CSV processed successfully',
    schema: {
      type: 'object',
      properties: {
        processed: { type: 'number', example: 150 },
        errors: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid CSV file or processing error' })
  @UseInterceptors(FileInterceptor('csvFile'))
  @HttpCode(HttpStatus.OK)
  async processCsv(
    @UploadedFile() file: Express.Multer.File,
    @Param('brokerId') brokerId: string
  ) {
    if (!file) {
      throw new Error('CSV file is required');
    }

    return await this.profitService.processCsvFile(file.buffer, brokerId);
  }

  @Post('process-array/:brokerId')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Process array of trading data for profit distribution',
    description: 'Process array of trading data objects to calculate and distribute profits for specific broker'
  })
  @ApiResponse({
    status: 200,
    description: 'Array data processed successfully',
    schema: {
      type: 'object',
      properties: {
        processed: { type: 'number', example: 150 },
        errors: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid data or processing error' })
  @HttpCode(HttpStatus.OK)
  async processArray(
    @Body() processArrayDto: ProcessArrayDto,
    @Param('brokerId') brokerId: string
  ) {
    return await this.profitService.processArrayData(
      processArrayDto.data, 
      brokerId, 
      processArrayDto.startDate, 
      processArrayDto.endDate
    );
  }

  @Get('my-profits')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get current user profits' })
  @ApiResponse({ status: 200, description: 'List of user profits' })
  getUserProfits(@CurrentUser() user: any) {
    return this.profitService.getUserProfits(user.userId);
  }

  @Get('my-summary')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get current user profit summary' })
  @ApiResponse({
    status: 200,
    description: 'User profit summary',
    schema: {
      type: 'object',
      properties: {
        totalUserProfit: { type: 'number' },
        totalReferralEarnings: { type: 'number' },
        totalTradingProfit: { type: 'number' },
        tradingAccounts: { type: 'number' },
        referralBonuses: { type: 'number' }
      }
    }
  })
  getUserSummary(@CurrentUser() user: any) {
    return this.profitService.getProfitSummary(user.userId);
  }

  @Get('all')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all profits (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of all profits with user details' })
  getAllProfits() {
    return this.profitService.getAllProfits();
  }

  @Get('user/:userId')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get specific user profits (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of user profits' })
  getSpecificUserProfits(@Param('userId') userId: string) {
    return this.profitService.getUserProfits(userId);
  }

  // Credit-Debit History Endpoints
  @Get('credit-debit-history/my-history')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get user credit-debit history' })
  @ApiResponse({ status: 200, description: 'List of user credit-debit transactions' })
  getMyCreditDebitHistory(@CurrentUser() user: any) {
    return this.profitService.getUserCreditDebitHistory(user.userId);
  }

  @Get('credit-debit-history/all')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all credit-debit history (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of all credit-debit transactions' })
  getAllCreditDebitHistory() {
    return this.profitService.getAllCreditDebitHistory();
  }

  @Get('credit-debit-history/source/:source')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get credit-debit history by source (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of credit-debit transactions by source' })
  getCreditDebitHistoryBySource(@Param('source') source: string) {
    return this.profitService.getCreditDebitHistoryBySource(source);
  }

  @Get('credit-debit-history/user/:userId')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get credit-debit history for specific user (Admin only)',
    description: 'Admin can view all credit-debit transactions of any user by providing user ID in params'
  })
  @ApiResponse({
    status: 200,
    description: 'List of user credit-debit transactions',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
          userId: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '507f1f77bcf86cd799439012' },
              firstName: { type: 'string', example: 'John' },
              lastName: { type: 'string', example: 'Doe' },
              email: { type: 'string', example: '<EMAIL>' }
            }
          },
          type: { type: 'string', enum: ['credit', 'debit'], example: 'credit' },
          amount: { type: 'number', example: 150.50 },
          firstname: { type: 'string', example: 'John' },
          lastname: { type: 'string', example: 'Doe' },
          accountNo: { type: 'string', example: '********' },
          source: { type: 'string', enum: ['referral', 'trade', 'withdraw', 'admin'], example: 'trade' },
          currency: { type: 'string', example: 'usdt' },
          description: { type: 'string', example: 'Trade profit from CSV processing - USDT 150.50' },
          balanceBefore: { type: 'number', example: 1000.00 },
          balanceAfter: { type: 'number', example: 1150.50 },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Only admin can view user credit-debit history / Target user not found' })
  getCreditDebitHistoryByUserId(@Param('userId') userId: string, @Request() req: any) {
    return this.profitService.getCreditDebitHistoryByUserId(userId, req.user.sub);
  }

  @Get('csv-history/:brokerId')
  @ApiBearerAuth('access-token')
  @ApiOperation({ 
    summary: 'Get CSV history for a specific broker',
    description: 'Retrieve all CSV processing history records for a specific broker'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'CSV history retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string' },
          brokerId: { type: 'string' },
          startDate: { type: 'string' },
          endDate: { type: 'string' },
          totalRebate: { type: 'number' },
          createdAt: { type: 'string' },
          updatedAt: { type: 'string' }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'No CSV history found for this broker' })
  getCsvHistory(@Param('brokerId') brokerId: string) {
    return this.profitService.getCsvHistory(brokerId);
  }

  @Post('verify-csv/:brokerId')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Verify CSV data before processing',
    description: 'Verify trading accounts exist for the broker before actual processing'
  })
  @ApiResponse({
    status: 200,
    description: 'CSV data verified successfully',
    schema: {
      type: 'object',
      properties: {
        validAccounts: { 
          type: 'array', 
          items: { type: 'string' },
          example: ['********', '********']
        },
        invalidAccounts: { 
          type: 'array', 
          items: { type: 'string' },
          example: ['********', '********']
        }
      }
    }
  })
  @HttpCode(HttpStatus.OK)
  async verifyCsv(
    @Body() processArrayDto: ProcessArrayDto,
    @Param('brokerId') brokerId: string
  ) {
    return await this.profitService.verifyCsvData(
      processArrayDto.data, 
      brokerId
    );
  }
}
