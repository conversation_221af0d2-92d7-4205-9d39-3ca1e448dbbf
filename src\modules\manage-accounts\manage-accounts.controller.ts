import { Controller, Get, Post, Body, Param, UseGuards, HttpStatus, HttpCode, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ManageAccountsService } from './manage-accounts.service';
import { JoinBrokerDto, JoinExchangeDto } from './dto/create-manage-account.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { CurrentUser } from '../../Gaurd/user.decorator';

@ApiTags('Manage Accounts')
@Controller('manage-accounts')
@UseGuards(JwtAuthGuard)
export class ManageAccountsController {
  constructor(private readonly manageAccountsService: ManageAccountsService) {}

  @Post('join-broker')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Join a broker with account number' })
  @ApiResponse({ status: 201, description: 'Successfully joined broker' })
  @ApiResponse({ status: 404, description: 'Broker or User not found' })
  @ApiResponse({ status: 409, description: 'User already joined this broker or account number exists' })
  @HttpCode(HttpStatus.CREATED)
  joinBroker(@Body() joinBrokerDto: JoinBrokerDto, @CurrentUser() user: any) {
    return this.manageAccountsService.joinBroker(joinBrokerDto, user.userId);
  }

  @Get('my-accounts')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get current user accounts' })
  @ApiResponse({ status: 200, description: 'List of user accounts with broker details' })
  getUserAccounts(@CurrentUser() user: any) {
    return this.manageAccountsService.getUserAccounts(user.userId);
  }

  @Get('broker/:brokerId')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all accounts for a specific broker' })
  @ApiResponse({ status: 200, description: 'List of broker accounts with user details' })
  getBrokerAccounts(@Param('brokerId') brokerId: string) {
    return this.manageAccountsService.getBrokerAccounts(brokerId);
  }

  @Get('user/:userId')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get all accounts for a specific user (Admin only)',
    description: 'Admin can view all accounts of any user by providing user ID in params'
  })
  @ApiResponse({
    status: 200,
    description: 'List of user accounts with broker details',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
          accountNo: { type: 'string', example: '********' },
          userId: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '507f1f77bcf86cd799439012' },
              firstName: { type: 'string', example: 'John' },
              lastName: { type: 'string', example: 'Doe' },
              email: { type: 'string', example: '<EMAIL>' }
            }
          },
          brokerId: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '507f1f77bcf86cd799439013' },
              brokerName: { type: 'string', example: 'XM Trading' },
              brokerType: { type: 'string', example: 'Forex' },
              brokerImage: { type: 'string', example: 'https://example.com/image.jpg' }
            }
          },
          status: { type: 'string', example: 'pending' },
          totalProfit: { type: 'number', example: 1250.50 },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Only admin can view user accounts' })
  @ApiResponse({ status: 404, description: 'Target user not found' })
  getUserAccountsByAdmin(@Param('userId') userId: string, @Request() req: any) {
    return this.manageAccountsService.getAccountsByUserId(userId, req.user.sub);
  }

  @Post('join-exchange')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Join an exchange with account number' })
  @ApiResponse({ status: 201, description: 'Successfully joined exchange' })
  @ApiResponse({ status: 404, description: 'Exchange or User not found' })
  @ApiResponse({ status: 409, description: 'User already joined this exchange or account number exists' })
  @HttpCode(HttpStatus.CREATED)
  joinExchange(@Body() joinExchangeDto: JoinExchangeDto, @CurrentUser() user: any) {
    return this.manageAccountsService.joinExchange(joinExchangeDto, user.userId);
  }

  @Get('my-exchange-accounts')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get current user exchange accounts' })
  @ApiResponse({ status: 200, description: 'List of user exchange accounts with exchange details' })
  getUserExchangeAccounts(@CurrentUser() user: any) {
    return this.manageAccountsService.getUserExchangeAccounts(user.userId);
  }
}
