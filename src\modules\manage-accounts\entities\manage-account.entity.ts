import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class ManageAccount extends Document {
  @Prop({ required: true })
  accountNo: string;

  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, ref: 'Broker' })
  brokerId: MongooseSchema.Types.ObjectId;

  @Prop({ default: 'pending' })
  status: string;

  @Prop({ default: Date.now })
  joinedAt: Date;

  @Prop({ default: 0 })
  totalProfit: number;

  @Prop({ default: 0 })
  firstname: string;

  @Prop({ default: 0 })
  lastname: string;

  @Prop({ default: '' })
  accountType: string;

  // @Prop({default: ''})
  // exchangeTitle: string;

  // @Prop({default: ''})
  // referralLink: string;
  
}

export const ManageAccountSchema = SchemaFactory.createForClass(ManageAccount);
