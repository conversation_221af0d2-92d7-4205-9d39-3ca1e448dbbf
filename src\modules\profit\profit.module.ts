import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ProfitService } from './profit.service';
import { ProfitController } from './profit.controller';
import { Profit, ProfitSchema } from './entities/profit.entity';
import { ReferralProfits, ReferralProfitsSchema } from './entities/referral-profits.entity';
import { CreditDebitHistory, CreditDebitHistorySchema } from './entities/credit-debit-history';
import { ManageAccount, ManageAccountSchema } from '../manage-accounts/entities/manage-account.entity';
import { Auth, AuthSchema } from '../auth/entities/auth.entity';
import { ReferralTree, ReferralTreeSchema } from '../auth/entities/referral-tree.entity';
import { DashboardModule } from '../dashboard/dashboard.module';
import { UserDashboardModule } from '../user-dashboard/user-dashboard.module';
import { CsvHistory, CsvHistorySchema } from './entities/csv-history.entity';
import { Broker, BrokerSchema } from '../broker/entity/broker';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Profit.name, schema: ProfitSchema },
      { name: ReferralProfits.name, schema: ReferralProfitsSchema },
      { name: CreditDebitHistory.name, schema: CreditDebitHistorySchema },
      { name: ManageAccount.name, schema: ManageAccountSchema },
      { name: Auth.name, schema: AuthSchema },
      { name: ReferralTree.name, schema: ReferralTreeSchema },
      { name: CsvHistory.name, schema: CsvHistorySchema },
      { name: Broker.name, schema: BrokerSchema },
    ]),
    DashboardModule,
    UserDashboardModule,
    NotificationsModule,
  ],
  controllers: [ProfitController],
  providers: [ProfitService],
  exports: [ProfitService]
})
export class ProfitModule {}
